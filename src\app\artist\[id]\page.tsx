import { ArtistDetailsPage } from "@/features/artist/components";
import { client } from "@/graphql-client";
import { getAllArtist } from "@/graphql/queries";

// Generate static params for static export
export async function generateStaticParams() {
  try {
    // Fetch artists from API for static generation
    const result = await client.graphql({
      query: getAllArtist,
      variables: {},
    });

    const artists = result?.data?.getAllArtist || [];
    return artists
      .filter((artist) => artist && typeof artist.id === 'string')
      .map((artist) => ({ id: artist.id }));
  } catch (error) {
    console.error("Error fetching artists for static generation:", error);
    // Return empty array if API fails during build
    return [];
  }
}

export default function ArtistDetails() {
  return <ArtistDetailsPage />;
}
