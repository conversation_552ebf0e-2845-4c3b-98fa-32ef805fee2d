"use client";
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import {
  MapPin,
  Calendar,
  Music,
  Edit3,
  UserPlus,
  LinkIcon,
  Play,
  Heart,
  MessageCircle,
  Share2,
  ThumbsUp,
} from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { client } from "@/graphql-client"
import { getAllArtist } from "@/graphql/queries"

export default function ArtistDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const artistId = params?.id as string;
  const [artist, setArtist] = useState<{
    id: string;
    name: string;
    bio: string;
    formedDate: string;
    disbandedDate: string | null;
    location: string;
    __typename: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArtist = async () => {
      setLoading(true);
      try {
        const result = await client.graphql({
          query: getAllArtist,
          variables: {},
        });

        // Process data regardless of errors
        processArtistData(result);

      } catch (error: unknown) {
        // Even if GraphQL throws an error, check if we have data
        const errorWithData = error as { data?: { getAllArtist?: unknown[] } };
        if (errorWithData?.data?.getAllArtist) {
          processArtistData(errorWithData);
        } else {
          console.error("True network error - no data available:", error);
          setArtist(null);
        }
      } finally {
        setLoading(false);
      }
    };

    const processArtistData = (result: { data?: { getAllArtist?: unknown[] } }) => {
      if (result?.data?.getAllArtist) {
        type ArtistType = {
          id: string;
          name: string;
          bio?: string | null;
          formedDate?: string | null;
          disbandedDate?: string | null;
          location?: string | null;
          __typename: string;
        };
        const artists = result.data.getAllArtist as ArtistType[];
        const found = artists.find((a) => a && a.id === artistId);
        if (found) {
          setArtist(found);
          console.log("✅ Successfully loaded artist:", found.name);
        } else {
          setArtist(null);
          console.log("❌ Artist not found with ID:", artistId);
        }
      } else {
        setArtist(null);
      }
    };

    if (artistId) fetchArtist();
  }, [artistId]);

  const handleSongClick = () => {
    router.push(`/song`);
  };
  const handleAlbumClick = () => {
    router.push(`/album`);
  };
  
  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }
  if (!artist) {
    return <div className="min-h-screen flex items-center justify-center text-red-500">Artist not found</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Banner Section */}
      <div className="relative h-64 md:h-80 w-full">
        <Image src="/dummy-image.png?height=320&width=1200" alt="Artist Banner" fill className="object-cover" />
        <div className="absolute inset-0 bg-black/20" />
      </div>

      {/* Profile Header */}
      <div className="relative px-4 md:px-8 lg:px-16">
        <div className="flex flex-col md:flex-row items-start md:items-end gap-4 -mt-16 md:-mt-20">
          <Avatar className="w-32 h-32 md:w-40 md:h-40 border-4 border-background">
            <AvatarImage src="/dummy-image.png?height=160&width=160" alt="Artist Profile" />
            <AvatarFallback className="text-2xl">{artist.name?.[0]?.toUpperCase() ?? "A"}</AvatarFallback>
          </Avatar>

          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl md:text-4xl font-bold">{artist.name}</h1>
              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{artist.location || "Unknown location"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Active since {artist.formedDate ? new Date(artist.formedDate).getFullYear() : "Unknown"}</span>
                </div>
              </div>
              {/* Genres/Badges can remain static or be replaced if available */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Pop</Badge>
                <Badge variant="secondary">Synthwave</Badge>
                <Badge variant="secondary">Electronic</Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button>
                <UserPlus className="w-4 h-4 mr-2" />
                Follow
              </Button>
              <Button variant="outline">
                <LinkIcon className="w-4 h-4 mr-2" />
                Connect
              </Button>
              <Button variant="outline">
                <Edit3 className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 md:px-8 lg:px-16 py-8 space-y-8">
        {/* About Section */}
        <Card>
          <CardHeader>
            <CardTitle>About</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">
              {artist.bio || "No biography available for this artist."}
            </p>
          </CardContent>
        </Card>

        {/* Experience / Discography Section */}
        <Card>
              <CardHeader>
                <CardTitle>Discography</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Albums List - Replace with actual album data */}
                <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
                  
                    <div key={"Neon"} className="space-y-2"
                    onClick={()=>handleAlbumClick()}>
                      <Image
                        src="/dummy-image.png?height=150&width=150"
                        alt={`Album Neon`}
                        width={150}
                        height={150}
                        className="rounded-md cursor-pointer"
                      />
                      <p className="font-medium">Neon Nightfall</p>
                      <p className="text-sm text-muted-foreground">Year 2020</p>
                    </div>
                    <div key={"Hearts"} className="space-y-2"
                    onClick={()=>handleAlbumClick()}
                    >
                      <Image
                        src="/dummy-image.png?height=150&width=150"
                        alt={`Album Neon`}
                        width={150}
                        height={150}
                        className="rounded-md cursor-pointer"
                      />
                      <p className="font-medium">Glass Hearts</p>
                      <p className="text-sm text-muted-foreground">Year 2017</p>
                    </div>
                 
                </div>
              </CardContent>
            </Card>

        {/* Popular Songs */}
        <Card>
          <CardHeader>
            <CardTitle>Popular Songs</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3 cursor-pointer"
             onClick={() => handleSongClick()}>
              <Play className="w-4 h-4 text-muted-foreground" />
              <div>
              <p className="font-medium">Electric Skin</p>
              <p className="text-sm text-muted-foreground">Neon Nightfall • 3:52</p> </div>
              </div>


            <div className="flex items-center justify-between"
            onClick={() => handleSongClick()}
            >
              <div className="flex items-center gap-3 cursor-pointer">
                <Play className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="font-medium">City Lights</p>
                  <p className="text-sm text-muted-foreground">Producer, Guitarist</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 cursor-pointer"
             onClick={() => handleSongClick()}>
              <Play className="w-4 h-4 text-muted-foreground" />
              <div>
                <p className="font-medium">Heartbeat Delay</p>
                <p className="text-sm text-muted-foreground">Glass Hearts • 3:45</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Credits & Collaborations */}
        <Card>
          <CardHeader>
            <CardTitle>Credits & Collaborations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="font-medium">Electric Skin</p>
              <p className="text-sm text-muted-foreground">
                Vocals, Writer • Produced by{" "}
                <span className="text-primary cursor-pointer hover:underline">Marcus Light</span>
              </p>
            </div>

            <Separator />

            <div className="space-y-2">
              <p className="font-medium">Heartbeat Delay</p>
              <p className="text-sm text-muted-foreground">
                Sound Engineer with{" "}
                <span className="text-primary cursor-pointer hover:underline">The Urban Collective</span>
              </p>
            </div>

            <Separator />

            <div className="space-y-2">
              <p className="font-medium">Acoustic Sessions Vol. 2</p>
              <p className="text-sm text-muted-foreground">
                Guest Performer with                 <span className="text-primary cursor-pointer hover:underline">Lisa Vane</span>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Influences */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Influenced By</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>RH</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Radiohead</span>
              </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>PF</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Pink Floyd</span>
              </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>AM</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Arctic Monkeys</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Influenced</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>LS</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Luna Smith</span>
              </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>TB</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">The Bridges</span>
                </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>NW</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Neon Waves</span>
              </div>
            </CardContent>
          </Card>
        </div>

         {/* Skills & Endorsements */}
        <Card>
          <CardHeader>
            <CardTitle>Skills & Endorsements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Rock Music</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Guitar</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Music Production</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Songwriting</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Feed</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex gap-4">
              <Avatar className="w-10 h-10">
                <AvatarImage src="/placeholder.svg?height=40&width=40" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-2">
                <p className="text-sm">
                  <span className="font-medium">John Doe</span> released a new single &ldquo;Electric Dreams&rdquo;
                </p>
                <p className="text-xs text-muted-foreground">2 days ago</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Heart className="w-4 h-4" />
                    <span>24</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <MessageCircle className="w-4 h-4" />
                    <span>8</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
              </div>
            </div>

            <Separator />

            <div className="flex gap-4">
              <Avatar className="w-10 h-10">
                <AvatarImage src="/placeholder.svg?height=40&width=40" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-2">
                <p className="text-sm">
                  <span className="font-medium">John Doe</span> collaborated with{" "}
                  <span className="text-primary cursor-pointer hover:underline">Sarah Mitchell</span> on a new track
                </p>
                <p className="text-xs text-muted-foreground">1 week ago</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Heart className="w-4 h-4" />
                    <span>42</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <MessageCircle className="w-4 h-4" />
                    <span>15</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Management Info */}
          {/* Stats */}
          <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Total Likes</span>
                <span>5.2K</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Monthly Listeners</span>
                <span>125K</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Total Plays</span>
                <span>2.1M</span>
              </div>
            </CardContent>
          </Card>

      
        <Card>
          <CardHeader>
            <CardTitle>Management Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium mb-2">Managed by:</p>
                <div className="flex items-center gap-3">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" />
                    <AvatarFallback>AM</AvatarFallback>
                  </Avatar>
                  <span className="text-primary cursor-pointer hover:underline">Alex Manager</span>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium mb-2">Page Contributors:</p>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src="/placeholder.svg?height=24&width=24" />
                      <AvatarFallback>SM</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-primary cursor-pointer hover:underline">Sarah Mitchell</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src="/placeholder.svg?height=24&width=24" />
                      <AvatarFallback>MR</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-primary cursor-pointer hover:underline">Mike Rodriguez</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        </div>
      </div>
    </div>
  )
}
