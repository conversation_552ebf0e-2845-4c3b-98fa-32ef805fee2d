"use client";
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import {
  MapPin,
  Calendar,
  Music,
  Edit3,
  UserPlus,
  LinkIcon,
  Play,
  Heart,
  MessageCircle,
  Share2,
  ThumbsUp,
} from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { client } from "@/graphql-client"
import { getArtistProfile } from "@/graphql/queries"

export default function ArtistDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const artistId = params?.id as string;
  const [artistProfile, setArtistProfile] = useState<{
    artist: {
      id: string;
      name: string;
      bio?: string | null;
      formedDate?: string | null;
      disbandedDate?: string | null;
      location?: string | null;
      __typename: string;
    };
    songs?: Array<{
      id: string;
      title?: string | null;
      duration?: number | null;
      recordID?: string | null;
      releaseDate?: string | null;
      coverPhoto?: string | null;
      role?: string | null;
      credits?: Array<{
        artistId: string;
        role: string;
      }>;
      __typename: string;
    }>;
    albums?: Array<{
      id: string;
      releaseDate?: string | null;
      genre?: Array<string | null> | null;
      description?: string | null;
      coverArtURL?: string | null;
      title?: string | null;
      __typename: string;
    }>;
    __typename: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArtist = async () => {
      setLoading(true);
      try {
        const result = await client.graphql({
          query: getArtistProfile,
          variables: {
            artistId:artistId
          },
        });

        // Process data regardless of errors
        processArtistData(result);

      } catch (error: unknown) {
        // Even if GraphQL throws an error, check if we have data
        const errorWithData = error as { data?: { getArtistProfile?: unknown } };
        if (errorWithData?.data?.getArtistProfile) {
          processArtistData(errorWithData);
        } else {
          console.error("True network error - no data available:", error);
          setArtistProfile(null);
        }
      } finally {
        setLoading(false);
      }
    };

    const processArtistData = (result: { data?: { getArtistProfile?: unknown } }) => {
      if (result?.data?.getArtistProfile) {
        type ArtistProfileType = {
          artist: {
            id: string;
            name: string;
            bio?: string | null;
            formedDate?: string | null;
            disbandedDate?: string | null;
            location?: string | null;
            __typename: string;
          };
          songs?: Array<{
            id: string;
            title?: string | null;
            duration?: number | null;
            recordID?: string | null;
            releaseDate?: string | null;
            coverPhoto?: string | null;
            role?: string | null;
            __typename: string;
          }>;
          albums?: Array<{
            id: string;
            releaseDate?: string | null;
            genre?: Array<string | null> | null;
            description?: string | null;
            coverArtURL?: string | null;
            title?: string | null;
            __typename: string;
          }>;
          __typename: string;
        };
        const profileData = result.data.getArtistProfile as ArtistProfileType;
        if (profileData?.artist) {
          setArtistProfile(profileData);
          console.log("✅ Successfully loaded artist:", profileData);
        } else {
          setArtistProfile(null);
          console.log("❌ Artist not found in profile data");
        }
      } else {
        setArtistProfile(null);
      }
    };

    if (artistId) fetchArtist();
  }, [artistId]);

  const handleSongClick = () => {
    router.push(`/song`);
  };
  const handleAlbumClick = () => {
    router.push(`/album`);
  };
  
  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }
  if (!artistProfile?.artist) {
    return <div className="min-h-screen flex items-center justify-center text-red-500">Artist not found</div>;
  }

  const artist = artistProfile.artist;
  const songs = artistProfile.songs || [];
  const albums = artistProfile.albums || [];

  return (
    <div className="min-h-screen bg-background">
      {/* Banner Section */}
      <div className="relative h-64 md:h-80 w-full">
        <Image src="/dummy-image.png?height=320&width=1200" alt="Artist Banner" fill className="object-cover" />
        <div className="absolute inset-0 bg-black/20" />
      </div>

      {/* Profile Header */}
      <div className="relative px-4 md:px-8 lg:px-16">
        <div className="flex flex-col md:flex-row items-start md:items-end gap-4 -mt-16 md:-mt-20">
          <Avatar className="w-32 h-32 md:w-40 md:h-40 border-4 border-background">
            <AvatarImage src="/dummy-image.png?height=160&width=160" alt="Artist Profile" />
            <AvatarFallback className="text-2xl">{artist.name?.[0]?.toUpperCase() ?? "A"}</AvatarFallback>
          </Avatar>

          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl md:text-4xl font-bold">{artist.name}</h1>
              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{artist.location || "Unknown location"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Active since {artist.formedDate ? new Date(artist.formedDate).getFullYear() : "Unknown"}</span>
                </div>
              </div>
              {/* Genres/Badges - can be updated with real data when available */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Pop</Badge>
                <Badge variant="secondary">Synthwave</Badge>
                <Badge variant="secondary">Electronic</Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button>
                <UserPlus className="w-4 h-4 mr-2" />
                Follow
              </Button>
              <Button variant="outline">
                <LinkIcon className="w-4 h-4 mr-2" />
                Connect
              </Button>
              <Button variant="outline">
                <Edit3 className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 md:px-8 lg:px-16 py-8 space-y-8">
        {/* About Section */}
        <Card>
          <CardHeader>
            <CardTitle>About</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">
              {artist.bio || "No biography available for this artist."}
            </p>
          </CardContent>
        </Card>

        {/* Experience / Discography Section */}
        <Card>
              <CardHeader>
                <CardTitle>Discography</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Albums List - Real API data */}
                <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
                  {albums.length > 0 ? (
                    albums.map((album) => (
                      <div key={album.id} className="space-y-2 cursor-pointer"
                        onClick={() => handleAlbumClick()}>
                        <Image
                          src={album.coverArtURL || "/dummy-image.png?height=150&width=150"}
                          alt={`Album ${album.title || "Unknown"}`}
                          width={150}
                          height={150}
                          className="rounded-md cursor-pointer"
                        />
                        <p className="font-medium">{album.title || "Unknown Album"}</p>
                        <p className="text-sm text-muted-foreground">
                          {album.releaseDate ? new Date(album.releaseDate).getFullYear() : "Unknown Year"}
                        </p>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted-foreground col-span-full">No albums available</p>
                  )}
                </div>
              </CardContent>
            </Card>

        {/* Songs */}
        <Card>
          <CardHeader>
            <CardTitle>Songs</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {songs.length > 0 ? (
              songs.map((song, index) => (
                <div key={song.id} className="space-y-2">
                  <div className="flex items-center gap-3">
                    <Play className="w-4 h-4 text-muted-foreground" />
                    <div className="flex-1">
                      <p className="font-medium">🎶 {song.title || "Unknown Song"}</p>
                      <div className="mt-1">
                        <p className="text-sm text-muted-foreground mb-1">👥 Credits:</p>
                        {song.credits && song.credits.length > 0 ? (
                          <div className="ml-4 space-y-1">
                            {song.credits.map((credit, creditIndex) => (
                              <p key={creditIndex} className="text-sm">
                                - {credit.artistId === artist.id ? (
                                  <span className="font-medium">{artist.name} ({credit.role})</span>
                                ) : (
                                  <span
                                    className="text-primary cursor-pointer hover:underline"
                                    onClick={() => router.push(`/artist/${credit.artistId}`)}
                                  >
                                    Artist ({credit.role}) 🡥
                                  </span>
                                )}
                              </p>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground ml-4">No credits available</p>
                        )}
                      </div>
                    </div>
                  </div>
                  {index < songs.length - 1 && <Separator />}
                </div>
              ))
            ) : (
              <p className="text-muted-foreground">No songs available</p>
            )}
          </CardContent>
        </Card>





        {/* Statistics */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Total Songs</span>
                <span>{songs.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Total Albums</span>
                <span>{albums.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Active Since</span>
                <span>{artist.formedDate ? new Date(artist.formedDate).getFullYear() : "Unknown"}</span>
              </div>
            </CardContent>
          </Card>


          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Artist Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Location</span>
                <span>{artist.location || "Not specified"}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Formed Date</span>
                <span>{artist.formedDate ? new Date(artist.formedDate).toLocaleDateString() : "Unknown"}</span>
              </div>
              {artist.disbandedDate && (
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Disbanded Date</span>
                  <span>{new Date(artist.disbandedDate).toLocaleDateString()}</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Influences */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Influenced By</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>RH</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Radiohead</span>
              </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>PF</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Pink Floyd</span>
              </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>AM</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Arctic Monkeys</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Influenced</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>LS</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Luna Smith</span>
              </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>TB</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">The Bridges</span>
              </div>
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src="/placeholder.svg?height=40&width=40" />
                  <AvatarFallback>NW</AvatarFallback>
                </Avatar>
                <span className="text-primary cursor-pointer hover:underline">Neon Waves</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Skills & Endorsements */}
        <Card>
          <CardHeader>
            <CardTitle>Skills & Endorsements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Rock Music</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Guitar</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Music Production</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>Songwriting</span>
                </div>
                <Button variant="outline" size="sm">
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  Endorse
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <Card>
          <CardHeader>
            <CardTitle>Activity Feed</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex gap-4">
              <Avatar className="w-10 h-10">
                <AvatarImage src="/placeholder.svg?height=40&width=40" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-2">
                <p className="text-sm">
                  <span className="font-medium">{artist.name}</span> released a new single &ldquo;Electric Dreams&rdquo;
                </p>
                <p className="text-xs text-muted-foreground">2 days ago</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Heart className="w-4 h-4" />
                    <span>24</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <MessageCircle className="w-4 h-4" />
                    <span>8</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
              </div>
            </div>

            <Separator />

            <div className="flex gap-4">
              <Avatar className="w-10 h-10">
                <AvatarImage src="/placeholder.svg?height=40&width=40" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-2">
                <p className="text-sm">
                  <span className="font-medium">{artist.name}</span> collaborated with{" "}
                  <span className="text-primary cursor-pointer hover:underline">Sarah Mitchell</span> on a new track
                </p>
                <p className="text-xs text-muted-foreground">1 week ago</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Heart className="w-4 h-4" />
                    <span>42</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <MessageCircle className="w-4 h-4" />
                    <span>15</span>
                  </button>
                  <button className="flex items-center gap-1 hover:text-foreground">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Management Info */}
        <Card>
          <CardHeader>
            <CardTitle>Management Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium mb-2">Managed by:</p>
                <div className="flex items-center gap-3">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" />
                    <AvatarFallback>AM</AvatarFallback>
                  </Avatar>
                  <span className="text-primary cursor-pointer hover:underline">Alex Manager</span>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium mb-2">Page Contributors:</p>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src="/placeholder.svg?height=24&width=24" />
                      <AvatarFallback>SM</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-primary cursor-pointer hover:underline">Sarah Mitchell</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src="/placeholder.svg?height=24&width=24" />
                      <AvatarFallback>MR</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-primary cursor-pointer hover:underline">Mike Rodriguez</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
