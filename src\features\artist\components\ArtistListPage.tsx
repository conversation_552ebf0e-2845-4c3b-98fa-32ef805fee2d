"use client"

import { useEffect, useState } from "react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Search, Users, Filter } from "lucide-react"
import { ArtistCard } from "./ArtistCard"
import { client } from "@/graphql-client"
import { getAllArtist } from "@/graphql/queries"
import { Artist } from "../types/artist"
import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from "next/navigation"


export default function ArtistsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [locationFilter, setLocationFilter] = useState<string>("all")
  const [artistApiData, setArtistApiData] = useState<Artist[]>([])
  const [loading, setLoading] = useState(false)
  const router = useRouter();


  // Fetch artists from API and update state
  const fetchArtists = async () => {
    setLoading(true)
    try {
      const result = await client.graphql({
        query: getAllArtist,
        variables: {},
      });

      // Handle the API response format with potential errors
      const apiData = result?.data?.getAllArtist || [];

      // Filter out any invalid artists and normalize the data
      const artists = apiData
        .filter((artist) => {
          if (typeof artist === 'object' && artist !== null) {
            const a = artist as Record<string, unknown>;
            return typeof a.id === 'string' && typeof a.name === 'string';
          }
          return false;
        })
        .map((artist) => {
          const a = artist as Record<string, unknown>;
          return {
            __typename: "Artist",
            id: String(a.id),
            name: String(a.name),
            bio: a.bio ? String(a.bio) : null,
            formedDate: a.formedDate ? String(a.formedDate) : null,
            disbandedDate: a.disbandedDate ? String(a.disbandedDate) : null,
            location: a.location ? String(a.location) : null,
          } as Artist;
        });

      setArtistApiData(artists);
      console.log("🚀 ~ fetchArtists result:", artists);

      // Log any errors from the API response
      if (result?.errors && result.errors.length > 0) {
        console.warn("API returned errors:", result.errors);
      }
    } catch (error) {
      console.error("Error fetching artists:", error);
      setArtistApiData([]); // Set empty array on error
    } finally {
      setLoading(false)
    }
  };

  // Call function on initial render
  useEffect(() => {
    fetchArtists();
  }, []);

  // Get unique locations for filter (filter out null/undefined values)
  const uniqueLocations = Array.from(
    new Set(
      artistApiData
        .map((artist) => artist.location)
        .filter((location): location is string => Boolean(location))
    )
  ).sort()

  // Filter artists based on search and filters
  const filteredArtists = artistApiData.filter((artist) => {
    const matchesSearch =
      artist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (artist.bio?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) ||
      (artist.location?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false)

    const matchesStatus =
      (statusFilter === "all") ||
      (statusFilter === "active" && !artist.disbandedDate) ||
      (statusFilter === "disbanded" && artist.disbandedDate)

    const matchesLocation = (locationFilter === 'all') || artist.location === locationFilter

    return matchesSearch && matchesStatus && matchesLocation
  })

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Users className="w-8 h-8 text-primary" />
            <h1 className="text-4xl font-bold">Artists</h1>
          </div>
          <p className="text-muted-foreground text-lg">
            Discover amazing artists from around the world. Browse through our collection of talented musicians and
            bands.
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search artists by name, bio, or location..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="disbanded">Disbanded</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={locationFilter} onValueChange={setLocationFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    {uniqueLocations.map((location) => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Filter Summary */}
            <div className="flex items-center gap-4 mt-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Filter className="w-4 h-4" />
                <span>
                  Showing {filteredArtists.length} of {artistApiData.length} artists
                </span>
              </div>
              {searchTerm && <span>• Searching for {searchTerm}</span>}
              {statusFilter && statusFilter !== "all" && <span>• Status: {statusFilter}</span>}
              {locationFilter && locationFilter !== "all" && <span>• Location: {locationFilter}</span>}
            </div>
          </CardContent>
        </Card>

        {/* Artists Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i}>
                <CardContent className="pt-6">
                  <Skeleton className="h-32 w-full mb-4" />
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredArtists.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredArtists.map((artist) => (
              <div key={artist.id} onClick={() => router.push(`/artist/${artist.id}`)} style={{ cursor: 'pointer' }}>
                <ArtistCard artist={artist} />
              </div>
            ))}
          </div>
        ) : (
          <Card className="text-center py-12">
            <CardContent>
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No artists found</h3>
              <p className="text-muted-foreground">
                {searchTerm || statusFilter || locationFilter
                  ? "Try adjusting your search criteria or filters."
                  : "No artists available at the moment."}
              </p>
            </CardContent>
          </Card>
        )}

        {/* Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">{artistApiData.length}</div>
              <div className="text-muted-foreground">Total Artists</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {artistApiData.filter((artist) => !artist.disbandedDate).length}
              </div>
              <div className="text-muted-foreground">Active Artists</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">{uniqueLocations.length}</div>
              <div className="text-muted-foreground">Countries/Regions</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
